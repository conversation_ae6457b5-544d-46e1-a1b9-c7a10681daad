package riskrecords

import (
	"encoding/json"

	"github.com/olivere/elastic"
)

type FofaQueryAddCondition map[string]interface{}

func ParseFofaQuery(fofaQuery string) (*FofaQueryAddCondition, error) {
	data := new(FofaQueryAddCondition)
	err := json.Unmarshal([]byte(fofaQuery), &data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (f *FofaQueryAddCondition) AddAggs(searchSource *elastic.SearchSource) *FofaQueryAddCondition {
	tmpAggInter, err := searchSource.Source()
	if err != nil {
		return f
	}
	if tmpAgg, ok := tmpAggInter.(map[string]interface{}); ok {
		(*f)["aggregations"] = tmpAgg["aggregations"]
		return f
	}
	return f
}

func (f *FofaQueryAddCondition) AddSource(searchSource *elastic.SearchSource) *FofaQueryAddCondition {
	tmpAggInter, err := searchSource.Source()
	if err != nil {
		return f
	}
	if tmpAgg, ok := tmpAggInter.(map[string]interface{}); ok {
		(*f)["_source"] = tmpAgg["_source"]
		return f
	}
	return f
}

func (f *FofaQueryAddCondition) AddSize(size int) *FofaQueryAddCondition {
	(*f)["size"] = size
	return f
}

func (f *FofaQueryAddCondition) AddSort(field string, ascending bool) *FofaQueryAddCondition {
	tmp := elastic.NewSearchSource().Sort(field, ascending)
	tmpAggInter, err := tmp.Source()
	if err != nil {
		return f
	}
	if tmpAgg, ok := tmpAggInter.(map[string]interface{}); ok {
		(*f)["sort"] = tmpAgg["sort"]
		return f
	}
	return f
}

func (f *FofaQueryAddCondition) ToString() (string, error) {
	tmp, err := json.Marshal(f)
	if err != nil {
		return "", err
	}
	return string(tmp), nil
}
