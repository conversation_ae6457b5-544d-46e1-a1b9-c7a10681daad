package riskrecords

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/olivere/elastic"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	"git.gobies.org/foeye/kingbase-driver"
	"git.gobies.org/foeye/shentong"
)

func connectMysql(options options) (*gorm.DB, error) {
	var db *gorm.DB
	var sqlDB *sql.DB
	var err error
	gormConfig := &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   "",   // 表名前缀，`User`表为`t_users`
			SingularTable: true, // 使用单数表名，启用该选项后，`User` 表将是`user`
		},
		Logger: logger.Default.LogMode(logger.Silent),
	}

	dbdsn := getDsn(options)

	// Gorm connections to database.
	if options.Driver == "OSCAR" {
		if db, err = gorm.Open(shentong.Open(dbdsn), gormConfig); err != nil {
			return nil, err
		}
	} else if options.Driver == "KingBase" {
		if db, err = gorm.Open(kingbase.Open(dbdsn), gormConfig); err != nil {
			return nil, err
		}
		//} else if options.Driver == "sqlite3" {
		//	if db, err = gorm.Open(sqlite.Open(dbdsn), gormConfig); err != nil {
		//		return nil, err
		//	}
	} else {
		if db, err = gorm.Open(mysql.Open(dbdsn), gormConfig); err != nil {
			return nil, err
		}
	}

	// Gets an connection pools of the current database connection instance.
	if sqlDB, err = db.DB(); err != nil {
		fmt.Println("=========")
		defer sqlDB.Close()
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	return db, nil
}

func getDsn(options options) string {
	dbdsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local&multiStatements=true",
		options.Username,
		options.Password,
		options.Host,
		options.Port,
		options.Dbname,
	)

	switch options.Driver {
	case "KingBase":
		dbdsn = fmt.Sprintf("host=%s port=%s user=%s "+
			"password=%s dbname=%s sslmode=disable",
			options.Host, options.Port, options.Username, options.Password, options.Dbname)
	case "OSCAR":
		dbdsn = fmt.Sprintf("%s:%s@%s:%s/%s?loc=Local",
			options.Username,
			options.Password,
			options.Host,
			options.Port,
			options.Dbname,
		)
	case "sqlite3", "sqlite":
		dbdsn = fmt.Sprintf("%s?_journal_mode=WAL&_busy_timeout=5000&_synchronous=NORMAL&_cache_size=-2000", options.Dbname)
	}

	return dbdsn
}

func closeMysql(db *gorm.DB) {
	instance, err := db.DB()
	if err != nil {
		return
	}
	instance.Close()
}

func connectEs(options options) (*elastic.Client, error) {
	client, err := elastic.NewClient(
		elastic.SetURL(options.ElasticHost),
		elastic.SetSniff(false),
		elastic.SetGzip(true),
		elastic.SetHealthcheckInterval(10*time.Second),
		elastic.SetMaxRetries(5),
	)
	if err != nil {
		return nil, err
	}
	return client, nil
}
