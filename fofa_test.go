package riskrecords

import (
	"log"
	"testing"

	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
)

func Test_ParseFofaQuery(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	log.Println(err)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)
}

func Test_AddAggs(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)

	testAgg := elastic.NewSearchSource().Aggregation("ip", elastic.NewTermsAggregation().Field("ip"))
	f := fofaQueryAddCondition.AddAggs(testAgg)
	assert.NotNil(t, f)

	testAgg1 := elastic.NewSearchSource()
	f1 := fofaQueryAddCondition.AddAggs(testAgg1)
	assert.NotNil(t, f1)
}

func Test_AddSource(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)

	testAgg := elastic.NewSearchSource().Aggregation("ip", elastic.NewTermsAggregation().Field("ip"))
	f := fofaQueryAddCondition.AddSource(testAgg)
	assert.NotNil(t, f)

	testAgg1 := elastic.NewSearchSource()
	f1 := fofaQueryAddCondition.AddSource(testAgg1)
	assert.NotNil(t, f1)
}

func Test_AddSize(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)

	f := fofaQueryAddCondition.AddSize(10)
	assert.NotNil(t, f)

	f1 := fofaQueryAddCondition.AddSize(0)
	assert.NotNil(t, f1)
}

func Test_AddSort(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)

	f := fofaQueryAddCondition.AddSort("ip", true)
	assert.NotNil(t, f)

	f1 := fofaQueryAddCondition.AddSort("", true)
	assert.NotNil(t, f1)
}

func Test_ToString(t *testing.T) {
	query := `{
  "constant_score": {
    "filter": {
      "bool": {
        "must": [
          {
            "bool": {
              "filter": {
                "match_phrase": {
                  "body": {
                    "query": "html"
                  }
                }
              }
            }
          }
        ]
      }
    }
  }
}
`
	fofaQueryAddCondition, err := ParseFofaQuery(query)
	assert.NoError(t, err)
	assert.NotNil(t, fofaQueryAddCondition)

	r, err := fofaQueryAddCondition.ToString()
	assert.NoError(t, err)
	assert.NotNil(t, r)
}
