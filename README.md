[![coverage report](https://git.gobies.org/foeye/riskrecords/badges/master/coverage.svg)](https://git.gobies.org/foeye/riskrecords/-/commits/master)

# riskrecords

计算资产风险相关信息

```shell
E:\riskrecords\cmd>go run . -h
Calculate asset risk related information

Usage:
  riskrecords [command]

Available Commands:
  riskrecords Calculate asset risk related information
  help        Help about any command
  completion  Generate the autocompletion script for the specified shell

Flags:
  -h, --help   help for riskrecords

Use "riskrecords [command] --help" for more information about a command.
```

```shell
E:\riskrecords\cmd>go run . riskrecords -h 
Calculate asset risk related information

Usage:
  riskrecords riskrecords [flags]

Flags:
      --dbname string         mysql数据库名称 (default "foeye3")
      --elastic_host string   es连接地址 (default "http://localhost:9200")
  -h, --help                  help for riskrecords
      --host string           mysql地址 (default "localhost")
      --password string       mysql密码 (default "database@secret@2020")
      --port string           mysql端口 (default "3306")
      --username string       mysql用户名 (default "root")

```

# 涉及项目

1. [foeyes](https://git.gobies.org/foeye/foeye3)
2. [foeye-engine-attribute-extraction]()
