package main

import (
	"log"
	"os"

	"git.gobies.org/foeye/riskrecords/v3"

	"github.com/spf13/cobra"
)

var (
	Host        string
	Port        string
	Username    string
	Password    string
	Dbname      string
	ElasticHost string
	Num         int
	Driver      string
)

// 初始化参数
func init() {
	WithImportRiskRecordsCommand.Flags().StringVar(&Host, "host", "localhost", "mysql地址")
	WithImportRiskRecordsCommand.Flags().StringVar(&Port, "port", "3306", "mysql端口")
	WithImportRiskRecordsCommand.Flags().StringVar(&Username, "username", "root", "mysql用户名")
	WithImportRiskRecordsCommand.Flags().StringVar(&Password, "password", "database@secret@2020", "mysql密码")
	WithImportRiskRecordsCommand.Flags().StringVar(&Dbname, "dbname", "foeye3", "mysql数据库名称")
	WithImportRiskRecordsCommand.Flags().StringVar(&ElasticHost, "elastic_host", "http://localhost:9200", "es连接地址")
	WithImportRiskRecordsCommand.Flags().IntVar(&Num, "num", 50, "工作线程数量")
	WithImportRiskRecordsCommand.Flags().StringVar(&Driver, "driver", "mysql", "驱动类型")
}

// RiskRecords provides handlers for risk records.
type RiskRecords struct {
}

// NewRiskRecords crate risk records instance
func NewRiskRecords() (*RiskRecords, error) {
	return &RiskRecords{}, nil
}

// WithImportRiskRecordsCommand 子命令实体
var WithImportRiskRecordsCommand = &cobra.Command{
	Use:   "riskrecords",
	Short: "Calculate asset risk related information",
	Run: func(cmd *cobra.Command, args []string) {
		api, err := NewRiskRecords()
		if err != nil {
			log.Println(err)
			os.Exit(1)
		}
		if err = api.start(); err != nil {
			log.Println(err)
			os.Exit(1)
		}
	},
}

// Start 启动子命令
func (d *RiskRecords) start() error {
	err := riskrecords.RiskRecords(
		Username,
		Password,
		Dbname,
		ElasticHost,
		riskrecords.WithHost(Host),
		riskrecords.WithPort(Port),
		riskrecords.WithNum(Num),
		riskrecords.WithDriver(Driver),
	)
	return err
}
