package riskrecords

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/olivere/elastic"
	"gorm.io/gorm"

	"git.gobies.org/foeye/riskrecords/v3/internal/services/pocs_service"

	"git.gobies.org/fofa-backend/fofacore"
)

type pocTask struct {
	basePoc
	body string
}

type PocQuery struct {
	Rule     string `json:"rule,omitempty"`    // 使用rule作为Poc的查询语句
	RuleId   uint   `json:"rule_id,omitempty"` // 使用rule_id作为Poc ID
	Product  string `json:"product"`           // 使用Product作为Poc Name
	Category string `json:"category"`          // 使用Category作为Poc Filename
}

type riskTask struct {
	Ip      string   `json:"ip"`
	Port    int      `json:"port"`
	VulFile string   `json:"vul_file"`
	Name    string   `json:"name"`
	Product []string `json:"products"`
}

type riskTaskRes struct {
	Ip      string `json:"ip"`
	Port    int    `json:"port"`
	VulFile string `json:"vul_file"`
	Name    string `json:"name"`
	Product string `json:"product"`
}

type WorkerGroup struct {
	num      int
	wg       *sync.WaitGroup
	esDb     *elastic.Client
	mysqlDb  *gorm.DB
	taskChan chan pocTask
}

const (
	PocQueryFile  = "./poc_query.json"
	ESDocuentSize = 1000 // 增加这个数，信创和国产化ＥＳ会报错：ES is dead
)

type pocRiskTaskRes struct {
	Count       int           `json:"count"`
	riskTaskRes []riskTaskRes `json:"riskTaskRes"`
	RealId      uint          `json:"real_id"`
	tableName   string        `json:"table_name"`
}

var PocRiskTaskRes map[uint]*pocRiskTaskRes

type CallbackFunc func([]*elastic.SearchHit)

func NewWorkerGroup(num int, esDb *elastic.Client, mysqlDb *gorm.DB) *WorkerGroup {
	return &WorkerGroup{
		num:      num,
		wg:       &sync.WaitGroup{},
		esDb:     esDb,
		mysqlDb:  mysqlDb,
		taskChan: make(chan pocTask, 1000),
	}
}

// LabeledQuery 带 ID 的查询结构
type LabeledQuery struct {
	ID      string
	Query   elastic.Query
	Name    string
	VulFile string
}

func containsEmptyField(data interface{}) bool {
	switch v := data.(type) {
	case map[string]interface{}:
		for k, val := range v {
			if k == "" {
				return true
			}
			if containsEmptyField(val) {
				return true
			}
		}
	case []interface{}:
		for _, item := range v {
			if containsEmptyField(item) {
				return true
			}
		}
	}
	return false
}

func (w *WorkerGroup) CalRiskRecordsCount(option options) {
	queries := pocs_service.GetFofaQuery(w.mysqlDb)
	var labeledQueries []LabeledQuery
	sqls := make([]string, 0)
	for _, query := range queries {
		qi := fofacore.QueryInput{
			Query: query.Fofaquery,
			Full:  true,
			Fraud: false,
		}
		out, _ := fofacore.ParseFEQuery(&qi)
		qr, ok := out.Query["query"]
		if !ok {
			continue
		}
		if containsEmptyField(qr) {
			log.Printf("Query [%d] has empty field name, skipping.\n name %s", query.ID, query.Name)
			continue
		}
		body, err := json.MarshalIndent(qr, "", "  ")
		if err != nil {
			log.Fatalf("failed to marshal query: %v", err)
		}
		labeledQueries = append(labeledQueries, LabeledQuery{
			ID:      strconv.Itoa(int(query.ID)),
			Query:   elastic.NewConstantScoreQuery(elastic.NewRawStringQuery(string(body))),
			Name:    query.Name,
			VulFile: query.Filename,
		})
	}

	multiSearch := w.esDb.MultiSearch().Index("fofaee_service", "fofaee_subdomain", "fofaee_siteurl")
	for _, q := range labeledQueries {
		multiSearch = multiSearch.Add(
			elastic.NewSearchRequest().Query(q.Query).Size(0),
		)
	}

	res, err := multiSearch.Do(context.Background())
	if err != nil {
		log.Fatalf("failed to execute msearch: %v", err)
	}

	var detailQueries []LabeledQuery
	for i, r := range res.Responses {
		label := labeledQueries[i].ID
		if r.Error != nil {
			fmt.Printf("Query [%s] error: %v\n", label, r.Error)
			continue
		}
		count := r.Hits.TotalHits
		//fmt.Printf("Query [%s] matched documents: %d\n", label, count)
		sqls = append(sqls, fmt.Sprintf("UPDATE pocs SET fofa_records = %d WHERE id = %s;", count, label))
		if count > 1 {
			detailQueries = append(detailQueries, labeledQueries[i])
		}
	}
	if len(sqls) > 0 {
		w.updatePocCount(sqls)
	}

	if option.Driver == "OSCAR" { // CIC版本不需要计算风险资产。
		return
	}

	// 异步处理漏洞风险资产记录
	go func() {
		// 单独初始化一个mysql连接，用于异步处理，防止外部结束将其关闭
		mysqlDb, err := connectMysql(option)
		if err != nil {
			log.Printf("[Riskrecords] second connectMysql failed: %v", err)
			return
		}
		defer closeMysql(mysqlDb)

		var taskResults []riskTaskRes
		ms := w.esDb.MultiSearch().Index("fofaee_service", "fofaee_subdomain", "fofaee_siteurl")

		for _, dq := range detailQueries {
			ms = ms.Add(
				elastic.NewSearchRequest().
					Query(dq.Query).
					Size(20000).
					FetchSourceContext(elastic.NewFetchSourceContext(true).Include("ip", "port", "product")),
			)
		}

		resp, err := ms.Do(context.Background())
		if err != nil {
			log.Printf("[Riskrecords] second msearch failed: %v", err)
			return
		}

		for i, r := range resp.Responses {
			if r.Error != nil || len(r.Hits.Hits) == 0 {
				log.Printf("[Riskrecords] result %d error: %v", i, r.Error)
				continue
			}
			for _, source := range r.Hits.Hits {
				var m map[string]interface{}
				if err := json.Unmarshal(*source.Source, &m); err != nil {
					log.Printf("unmarshal hit source failed: %v", err)
					continue
				}

				ip, _ := m["ip"].(string)
				port, _ := m["port"].(float64)
				product, _ := m["product"].(string)

				dq := detailQueries[i]
				taskResults = append(taskResults, riskTaskRes{
					Ip:      ip,
					Port:    int(port),
					Product: product,
					Name:    dq.Name,
					VulFile: dq.VulFile,
				})
			}
		}

		if len(taskResults) > 0 {
			err := mysqlDb.Table("assets_risk").CreateInBatches(taskResults, 3000).Error
			if err != nil {
				log.Printf("[Riskrecords] UpdatePocData CreateInBatches err:%v", err)
			}
		}
		log.Printf("[Riskrecords] total taskResults: %d", len(taskResults))
	}()
}

func (w *WorkerGroup) updatePocCount(sqls []string) {
	sql := strings.Join(sqls, "\n")
	//log.Println("[Riskrecords] UpdatePocData sql:", sql)
	err := w.mysqlDb.Exec(sql).Error
	if err != nil {
		log.Printf("[Riskrecords] UpdatePocData poc count err:%v", err)
		time.Sleep(time.Second * 1)
		// 重试一次
		w.mysqlDb.Exec(sql)
	}
}
