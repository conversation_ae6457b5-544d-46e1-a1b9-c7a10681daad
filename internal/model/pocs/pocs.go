package pocs

import (
	"time"

	"gorm.io/gorm"
)

type Poc struct {
	ID               uint       `gorm:"type:int(11);unsigned; auto_increment;primaryKey;not null;comment:记录ID"`
	Name             string     `gorm:"type:varchar(1000);comment:poc名称;"`
	Description      string     `gorm:"type:text;comment:描述;"`
	Filename         string     `gorm:"type:varchar(255);default:null;comment:文件名;uniqueIndex"`
	Author           string     `gorm:"column:author;type:varchar(255);comment:作者;"`
	Product          string     `gorm:"type:varchar(255);comment:对应产品;"`
	Homepage         string     `gorm:"type:varchar(1000);comment:产品主页;"`
	References       string     `gorm:"column:references;type:text;comment:引用地址;" `
	Fofaquery        string     `gorm:"column:fofaquery;type:text;comment:查询语句;"`
	Content          string     `gorm:"column:content;type:text;comment:漏洞验证参数;"`
	State            uint       `gorm:"column:state;type:int(11);default:0;comment:状态;"`
	UserID           uint       `gorm:"column:user_id;type:int(11);comment:用户ID;"`
	Price            uint       `gorm:"column:price;type:int(11);default:0;comment:价格;"`
	Score            uint       `gorm:"column:score;type:int(11);default:0;comment:评分;"`
	CommentsCount    uint       `gorm:"column:comments_count;type:int(11);default:0;comment:评论数;"`
	FofaRecords      uint       `gorm:"column:fofa_records;type:int(11);default:0;comment:fofa影响ip数;"`
	Level            uint8      `gorm:"column:level;type:tinyint(4);default:3;comment:等级;"`
	XdaySt           string     `gorm:"column:xday_st;type:varchar(100);default:0;comment:0dya,nday;"`
	AffectAmount     uint       `gorm:"column:affect_amount;type:int(11);default:0;comment:影响总量;"`
	AffectEnterprise string     `gorm:"column:affect_enterprise;type:text;comment:影响企业;"`
	RejectReason     string     `gorm:"column:reject_reason;type:varchar(500);comment:拒绝理由;"`
	VulDefine        string     `gorm:"column:vul_define;type:varchar(100);comment:漏洞定义;"`
	ProductionDefine string     `gorm:"column:production_define;type:varchar(100);comment:产品定义;"`
	PriceDefine      string     `gorm:"column:price_define;type:varchar(100);comment:价格定义;"`
	TaskState        uint       `gorm:"column:task_state;type:int(11);default:0;comment:任务状态;"`
	TaskPercent      string     `gorm:"column:task_percent;type:varchar(10);comment:任务进度;"`
	CreatedAt        *time.Time `gorm:"type:timestamp;default:current_timestamp;comment:添加时间;"`
	UpdatedAt        *time.Time `gorm:"type:timestamp;null;comment:更新时间;"`
	LastScanedAt     *time.Time `gorm:"column:last_scaned_at;type:datetime;null;comment:最后扫描时间;"`
	LastTid          float64    `gorm:"column:last_tid;type:decimal(20,0);comment:最新任务ID;"`
	Impact           string     `gorm:"column:impact;type:mediumtext;comment:漏洞危害;"`
	Recommandation   string     `gorm:"column:recommandation;type:mediumtext;comment:修复建议;"`
	HasExp           bool       `gorm:"column:has_exp;type:tinyint(1);default:0;comment:是否可验证;"`
	ThreatCount      uint       `gorm:"column:threat_count;type:int(11);comment:漏洞数;"`
	ScanTaskID       uint       `gorm:"column:scan_task_id;type:int(11);comment:扫描任务ID;"`
	BeginScanTime    *time.Time `gorm:"column:begin_scan_time;type:datetime;comment:开始扫描时间;"`
	CveID            string     `gorm:"column:cveId;type:varchar(2000);comment:Cve编号;"`
	VulType          string     `gorm:"column:vulType;type:varchar(2000);default:null;comment:漏洞类型;"`
	Tags             string     `gorm:"column:tags;type:varchar(255);default:null;comment:漏洞类型及分类;"`
	DisclosureDate   *time.Time `gorm:"column:disclosure_date;type:datetime;comment:披露;"`
	VulNum           string     `gorm:"uniqueIndex;column:vulNum;type:varchar(255);comment:内部漏洞编号;"` // 自定义唯一标识
}

func New() *Poc {
	return &Poc{}
}

func (p *Poc) TableName() string {
	return "pocs"
}

func (p *Poc) FofaQueryList(db *gorm.DB) (queries []struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Fofaquery string `json:"fofaquery"`
	Filename  string `json:"filename"`
}) {

	err := db.Model(&Poc{}).Select("id", "fofaquery", "name", "filename").Find(&queries).Error
	if err != nil {
		return nil
	}
	return queries
}
