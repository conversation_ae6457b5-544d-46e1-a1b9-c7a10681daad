package riskrecords

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

type basePoc struct {
	Table       string
	Id          uint
	FofaQuery   string
	FofaRecords int
	Filename    string
	Name        string
}

type poc struct {
	Id          uint   `json:"id"`
	Fofaquery   string `json:"fofaquery"`
	Filename    string `json:"filename"`
	FofaRecords int    `json:"fofa_records"`
	Name        string `json:"name"`
}

type customPoc struct {
	Id          uint   `json:"id"`
	FofaQuery   string `json:"fofa_query"`
	FofaRecords int    `json:"fofa_records"`
	Name        string `json:"name"`
}

// RiskRecords 计算资产风险相关信息
func RiskRecords(username, password, dbname, elasticHost string, option ...option) error {
	options := GetOption(username, password, dbname, elasticHost, option)

	// 连接mysql
	mysqlDb, err := connectMysql(options)
	if err != nil {
		return err
	}
	defer closeMysql(mysqlDb)

	// 连接es
	esDb, err := connectEs(options)
	if err != nil {
		return err
	}

	// 获取所有poc
	pocs, err := GetPocs(mysqlDb)
	if err != nil {
		return err
	}

	// 获取所有自定义poc
	customPocs, err := GetCustomPocs(mysqlDb)
	if err != nil {
		return err
	}

	// 组合预置poc和自定义poc为基础poc
	var basePocs []basePoc
	for _, val := range pocs {
		tmp := basePoc{
			Table:       "pocs",
			Id:          val.Id,
			FofaQuery:   val.Fofaquery,
			FofaRecords: val.FofaRecords,
			Filename:    val.Filename,
			Name:        val.Name,
		}
		basePocs = append(basePocs, tmp)
	}
	for _, val := range customPocs {
		tmp := basePoc{
			Table:       "custom_pocs",
			Id:          val.Id,
			FofaQuery:   val.FofaQuery,
			FofaRecords: val.FofaRecords,
			Filename:    fmt.Sprintf("custom_poc_%d.go", val.Id),
			Name:        val.Name,
		}
		basePocs = append(basePocs, tmp)
	}

	ClearAssetRisk(mysqlDb)
	wg := NewWorkerGroup(options.Num, esDb, mysqlDb)

	time1 := time.Now()
	wg.CalRiskRecordsCount(options)
	time2 := time.Now()
	duration := time2.Sub(time1)
	log.Println("[Time] CalRiskRecordsCount time use :", "duration", duration)

	return nil
}

func ClearAssetRisk(mysqlDb *gorm.DB) {
	err := mysqlDb.Exec("TRUNCATE TABLE assets_risk").Error
	if err != nil {
		log.Println(err)
	}
}

func GetCustomPocs(mysqlDb *gorm.DB) (customPocs []customPoc, err error) {
	err = mysqlDb.Table("custom_pocs").Select("id", "fofa_query", "name", "fofa_records").Find(&customPocs).Error
	if err != nil {
		return nil, err
	}
	return customPocs, err
}

func GetPocs(mysqlDb *gorm.DB) (pocs []poc, err error) {
	err = mysqlDb.Table("pocs").Select("id", "fofaquery", "filename", "name", "fofa_records").Find(&pocs).Error
	if err != nil {
		return nil, err
	}
	return pocs, err
}

func GetOption(username string, password string, dbname string, elasticHost string, option []option) options {
	options := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    username,
		Password:    password,
		Dbname:      dbname,
		ElasticHost: elasticHost,
		Num:         50,
	}

	for _, o := range option {
		o.Set(&options)
	}
	return options
}

// WithHost 设置mysql host
func WithHost(host string) option {
	return optionFunc(func(options *options) {
		options.Host = host
	})
}

// WithPort 设置mysql端口
func WithPort(port string) option {
	return optionFunc(func(options *options) {
		options.Port = port
	})
}

// WithNum 工作线程数量
func WithNum(num int) option {
	return optionFunc(func(options *options) {
		options.Num = num
	})
}

// WithDriver 驱动
func WithDriver(driver string) option {
	return optionFunc(func(options *options) {
		options.Driver = driver
	})
}
