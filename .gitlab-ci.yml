before_script:
  - cp /home/<USER>/engine_configs/risk.test.toml conf.test.toml
  - cp /home/<USER>/engine_configs/risk.test.toml conf.toml
  - go env -w GOPROXY="https://goproxy.cn,direct"
  - export APPLICATION_MODE=test
  - go mod tidy

after_script:
  - rm -rf ~/.netrc
  - |
    if [ "$CI_JOB_STATUS" == "failed" ]; then
       V_TEXT="**CI任务<font color=\\\"#FF3333\\\">执行失败</font>通知**${V_BR}\
          **任务ID**: **${CI_JOB_ID}**${V_BR}\
          **任务名**: **${CI_JOB_NAME}**${V_BR}\
          **项目**: **${CI_PROJECT_PATH}**${V_BR}\
          **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
          **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
          "

       curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI执行失败通知\",\"text\":\"${V_TEXT}\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
    fi

variables:
  # Disable shallow cloning so that goreleaser can diff between tags to
  # generate a changelog.
  GIT_DEPTH: ''
  # 钉钉markdown换行符 必须\n且前后跟两个空格(shell 转义)
  V_BR: "\ \ \\n\ \ "

#job:
#  tags:
#    - foeye3runner
#  script: 
#    - go env 
#    - go test ./... -p 1 -coverprofile=coverage.out
#    - go tool cover -func=coverage.out
stages:
  - test

#unit_tests:
#  stage: test
#  script: 
#    - make test
#  tags: 
#    - foeye3runner

code_coverage:
  stage: test
  interruptible: false
  coverage: '/\s+\(statements\)\s+(\d+\.\d+)%/'
  script:
    - make test
    - make coverage
    - |
      if [ -f "coverage.out" ]; then
          coverage=$(go tool cover -func=coverage.out | awk '/^total:/ {print substr($3, 1, length($3)-1)}')
          if (( $(echo "$coverage < $PROGRESS" | bc -l) )); then
              V_TEXT="**<font color=\\\"#FF3333\\\">单元测试覆盖<$PROGRESS%通知</font>**${V_BR}\
                **任务ID**: **${CI_JOB_ID}**${V_BR}\
                **任务名**: **${CI_JOB_NAME}**${V_BR}\
                **项目**: **${CI_PROJECT_PATH}**${V_BR}\
                **分支**: **${CI_BUILD_REF_NAME}**${V_BR}\
                **覆盖度**: **<font color=\\\"#FF3333\\\">$coverage%</font>**${V_BR}\
                **执行人**: **${GITLAB_USER_NAME}**${V_EXTRA}\
                "

              curl -XPOST "https://oapi.dingtalk.com/robot/send?access_token=${DINGTALK_TOKEN}" -H 'Content-Type: application/json'  -d "{\"actionCard\":{\"title\":\"CI 单元测试覆盖度不达标通知\",\"text\":\"$V_TEXT\",\"btnOrientation\":\"1\",\"btns\":[{\"title\":\"查看详情\",\"actionURL\":\"dingtalk://dingtalkclient/page/link?url=${CI_PROJECT_URL}/pipelines/${CI_PIPELINE_ID}&pc_slide=false\"}]},\"msgtype\":\"actionCard\"}"
          fi
        else
          echo "No coverage report found."
      fi
  tags: 
    - Foeye3
  artifacts:
    when: always
    reports:
      junit: junit.xml
      coverage_report:
        coverage_format: cobertura
        path: cobertura.xml
