package riskrecords

type option interface {
	Set(options *options)
}

type optionFunc func(*options)

func (f optionFunc) Set(options *options) {
	f(options)
}

type options struct {
	Driver      string // ""
	Host        string // "localhost"
	Port        string // "3306"
	Username    string // "root"
	Password    string // "database@secret@2020"
	Dbname      string // "foeye3"
	ElasticHost string // "http://localhost:9200"
	Num         int    // 50
}
