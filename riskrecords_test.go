package riskrecords

import (
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"git.gobies.org/foeye/riskrecords/v3/internal/services/pocs_service"
)

func TestRiskRecords(t *testing.T) {
	wg, _ := SetWorkerGroup(t)

	var pocs []poc
	gk := gomonkey.ApplyFuncReturn(GetPocs, pocs, nil)
	defer gk.Reset()

	var customPocs []customPoc
	gk2 := gomonkey.ApplyFuncReturn(GetCustomPocs, customPocs, nil)
	defer gk2.Reset()

	gk3 := gomonkey.ApplyFuncReturn(ClearAssetRisk)
	defer gk3.Reset()

	m1 := gomonkey.ApplyFuncReturn(elastic.NewClient, &elastic.Client{}, nil)
	m2 := gomonkey.ApplyFuncReturn(connectMysql, &gorm.DB{}, nil)
	m3 := gomonkey.ApplyFuncReturn(closeMysql)
	queries := []struct {
		ID        uint   `json:"id"`
		Name      string `json:"name"`
		Fofaquery string `json:"fofaquery"`
		Filename  string `json:"filename"`
	}{}
	m4 := gomonkey.ApplyFuncReturn(pocs_service.GetFofaQuery, queries)

	defer gomonkey.ApplyMethodReturn(wg, "CalRiskRecordsCount").Reset()

	err := RiskRecords("root", "", "fofaee", "http://localhost:9200", WithNum(6), WithPort("3306"), WithHost("127.0.0.1"))
	assert.NoError(t, err)
	m1.Reset()
	m2.Reset()
	m3.Reset()
	m4.Reset()

	err = RiskRecords("", "", "fofaee", "http://localhost:9200", WithNum(6), WithPort("3306"), WithHost("127.0.0.1"))
	assert.Error(t, err)

	err = RiskRecords("root", "", "fofaee", "")
	assert.Error(t, err)
}

func TestWithHost(t *testing.T) {
	var o = new(options)
	option := WithHost("127.0.0.1")
	option.Set(o)

	assert.Equal(t, "127.0.0.1", o.Host)
}

func TestWithPort(t *testing.T) {
	var o = new(options)
	option := WithPort("80")
	option.Set(o)

	assert.Equal(t, "80", o.Port)
}

func TestWithNum(t *testing.T) {
	var o = new(options)
	option := WithNum(80)
	option.Set(o)

	assert.Equal(t, 80, o.Num)
}
