package riskrecords

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/olivere/elastic"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/stretchr/testify/assert"
)

func SetWorkerGroup(t *testing.T) (*WorkerGroup, *elastic.Client) {
	opt := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9200",
		Num:         50,
	}

	db := &gorm.DB{}
	defer gomonkey.ApplyFuncReturn(gorm.Open, db, nil).Reset()
	sqlDb := &sql.DB{}
	defer gomonkey.ApplyMethodReturn(db, "DB", sqlDb, nil).Reset()
	defer gomonkey.ApplyMethodReturn(sqlDb, "Close", nil).Reset()

	// 连接mysql
	mysqlDb, err := connectMysql(opt)
	assert.NoError(t, err)
	assert.NotNil(t, mysqlDb)
	defer closeMysql(mysqlDb)

	defer gomonkey.ApplyFuncReturn(elastic.NewClient, &elastic.Client{}, nil).Reset()
	// 连接es
	esDb, err := connectEs(opt)
	assert.NoError(t, err)
	assert.NotNil(t, esDb)

	wg := NewWorkerGroup(10, esDb, mysqlDb)
	assert.NotNil(t, wg)
	return wg, esDb
}

type mockMultiSearchService struct {
	resp *elastic.MultiSearchResult
}

func (m *mockMultiSearchService) Index(indices ...string) *mockMultiSearchService {
	return m
}
func (m *mockMultiSearchService) Add(reqs ...*elastic.SearchRequest) *mockMultiSearchService {
	return m
}
func (m *mockMultiSearchService) Do(ctx context.Context) (*elastic.MultiSearchResult, error) {
	return m.resp, nil
}

func toRawMessage(m map[string]interface{}) *json.RawMessage {
	b, _ := json.Marshal(m)
	raw := json.RawMessage(b)
	return &raw
}

type mockSearchService struct {
	resp *elastic.MultiSearchResult
}

func (m *mockSearchService) Index(indices ...string) *elastic.MultiSearchService {
	return &elastic.MultiSearchService{}
}
func (m *mockSearchService) Add(reqs ...*elastic.SearchRequest) *elastic.MultiSearchService {
	return &elastic.MultiSearchService{}
}

func TestCalRiskRecordsCount(t *testing.T) {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	assert.NoError(t, err)
	db.Exec(`CREATE TABLE IF NOT EXISTS pocs (
        id INTEGER PRIMARY KEY,
        name TEXT,
        fofaquery TEXT,
        filename TEXT
    )`)
	db.Exec("insert into pocs (id, name, fofaquery, filename) values (?, ?, ?, ?)", 1, "poc1", "fofaquery", "filename")

	// fake elastic result
	mockResp := &elastic.MultiSearchResult{
		Responses: []*elastic.SearchResult{
			{
				Hits: &elastic.SearchHits{
					TotalHits: 3,
					Hits: []*elastic.SearchHit{
						{Source: toRawMessage(map[string]interface{}{"ip": "*******"})},
					},
				},
			},
		},
	}

	// mock MultiSearchService.Do
	patches := gomonkey.NewPatches()
	defer patches.Reset()

	patches.ApplyMethodFunc(
		(*elastic.MultiSearchService)(nil),
		"Do",
		func(ctx context.Context) (*elastic.MultiSearchResult, error) {
			return mockResp, nil
		},
	)

	patches.ApplyMethodFunc(
		(*elastic.MultiSearchService)(nil),
		"Index",
		func(s *elastic.MultiSearchService, indices ...string) *elastic.MultiSearchService {
			return s
		},
	)

	patches.ApplyMethodFunc(
		(*elastic.MultiSearchService)(nil),
		"Add",
		func(s *elastic.MultiSearchService, reqs ...*elastic.SearchRequest) *elastic.MultiSearchService {
			return s
		},
	)

	// mock elastic.Client.MultiSearch
	es := &elastic.Client{}
	patches.ApplyMethodFunc(
		(*elastic.Client)(nil),
		"MultiSearch",
		func(c *elastic.Client) *elastic.MultiSearchService {
			return &elastic.MultiSearchService{}
		},
	)

	// 调用测试目标
	wg := NewWorkerGroup(10, es, db)
	wg.CalRiskRecordsCount(options{})
}
