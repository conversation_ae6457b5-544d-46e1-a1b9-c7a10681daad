package riskrecords

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_connectMysql(t *testing.T) {
	opt := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9200",
		Num:         50,
	}

	db, err := connectMysql(opt)
	assert.NoError(t, err)
	assert.NotNil(t, db)
}

func Test_connectMysql_Fail(t *testing.T) {
	opt := options{
		Host:        "localhost",
		Port:        "3307",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9200",
		Num:         50,
	}
	db, err := connectMysql(opt)
	assert.Error(t, err)
	assert.Nil(t, db)
}

func Test_closeMysql(t *testing.T) {
	opt := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9200",
		Num:         50,
	}
	db, err := connectMysql(opt)
	assert.NoError(t, err)
	assert.NotNil(t, db)

	closeMysql(db)
}

func Test_connectEs(t *testing.T) {
	opt := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9200",
		Num:         50,
	}
	c, err := connectEs(opt)
	assert.NoError(t, err)
	assert.NotNil(t, c)
}

func Test_connectEs_fail(t *testing.T) {
	opt := options{
		Host:        "localhost",
		Port:        "3306",
		Username:    "root",
		Password:    "",
		Dbname:      "fofaee",
		ElasticHost: "http://127.0.0.1:9201",
		Num:         50,
	}
	c, err := connectEs(opt)
	assert.Error(t, err)
	assert.Nil(t, c)
}
