Go ?= go

dependence:
	$(Go) mod tidy
	$(Go) mod vendor

# Cli application build.
.PHONY: test
test: dependence
	$(Go) test -race -p 1 ./... -coverprofile=coverage.out -v 2>&1 | tee source.out

.PHONY: coverage
coverage:
	cat source.out | go-junit-report -set-exit-code > junit.xml
	$(Go) tool cover -func=coverage.out
	gocover-cobertura < coverage.out > cobertura.xml

# Cli application install.
.PHONY: install
install: dependence
	$(Go) install .

# Cli application build.
.PHONY: build
build: dependence
	$(Go) build .
